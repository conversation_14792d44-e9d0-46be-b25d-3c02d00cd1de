{"cells": [{"cell_type": "code", "execution_count": 2, "id": "f81471d5-be93-498e-8f23-75dc308a0cc0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/CAA/venv/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import torch as t\n", "from llama_wrapper import LlamaWrapper\n", "import os\n", "from dotenv import load_dotenv\n", "from matplotlib import pyplot as plt\n", "from IPython.display import display, HTML\n", "import matplotlib\n", "from utils.tokenize import tokenize_llama_chat\n", "from behaviors import get_steering_vector, ALL_BEHAVIORS"]}, {"cell_type": "code", "execution_count": 3, "id": "16f9f7e2-a8c9-4b21-ad2f-fe404c31abef", "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "HUGGINGFACE_TOKEN = os.getenv(\"HF_TOKEN\")"]}, {"cell_type": "code", "execution_count": 4, "id": "8032e328-6125-46b9-81b3-ecc4bd90c6cf", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|██████████| 2/2 [00:53<00:00, 26.55s/it]\n", "/root/CAA/venv/lib/python3.10/site-packages/transformers/utils/hub.py:374: FutureWarning: The `use_auth_token` argument is deprecated and will be removed in v5 of Transformers.\n", "  warnings.warn(\n", "Downloading generation_config.json: 100%|██████████| 188/188 [00:00<00:00, 607kB/s]\n"]}], "source": ["model = LlamaWrapper(hf_token=HUGGINGFACE_TOKEN, size='7b')"]}, {"cell_type": "markdown", "id": "6b5b7a1f-b430-448b-9cc9-4a315e821130", "metadata": {}, "source": ["# Calculating dot products between steering vectors and activations"]}, {"cell_type": "markdown", "id": "337348a0", "metadata": {}, "source": ["## Helpers"]}, {"cell_type": "code", "execution_count": 16, "id": "0933aea8-0df5-446f-8d88-01bb1b46b8f8", "metadata": {}, "outputs": [], "source": ["def value_to_color(value, cmap=plt.cm.RdBu, vmin=-25, vmax=25):\n", "    # Convert value to a range between 0 and 1\n", "    norm = plt.Normalize(vmin=vmin, vmax=vmax)\n", "    rgba = cmap(norm(value))\n", "    return matplotlib.colors.to_hex(rgba)\n", "\n", "\n", "def display_token_dot_products(data):\n", "    html_content = \"<style>del, s, strike, .line-through { text-decoration: none !important; } .whitebg { background: white; color: black; padding: 15px; }</style><div class='whitebg'>\"\n", "    max_dist_from_zero = max([abs(x[1]) for x in data])\n", "    for token, value in data:\n", "        color = value_to_color(value, vmin=-1 * max_dist_from_zero, vmax=max_dist_from_zero)\n", "        html_content += f\"<span style='background-color: {color}; padding: 2px 5px; display: inline-block;'>{token} ({value:.2f})</span>\"\n", "    html_content += \"</div>\"\n", "    display(HTML(html_content))\n", "    \n", "def display_token_dot_products_final_text(data, text, tokenizer, show_sim=False):\n", "    html_content = \"<style>del, s, strike, .line-through { text-decoration: none !important; } .whitebg { background: white; color: black; padding: 15px; font-size: 20px; }</style><div class='whitebg'>\"\n", "    max_dist_from_zero = max([abs(x[1]) for x in data])\n", "    tokens = tokenizer.encode(text)\n", "    tokens = tokenizer.batch_decode(t.tensor(tokens).unsqueeze(-1))\n", "    for idx, (_, value) in enumerate(data):\n", "        color = value_to_color(value, vmin=-1 * max_dist_from_zero, vmax=max_dist_from_zero)\n", "        if len(tokens[idx].strip()) == 0:\n", "            html_content += \"<span> </span>\"\n", "            continue\n", "        if show_sim:\n", "            html_content += f\"<span style='background-color: {color}; padding: 2px 5px; display: inline-block;'>{tokens[idx].strip()} ({value:.2f})</span>\"\n", "        else:\n", "            html_content += f\"<span style='background-color: {color}; padding: 2px 5px; display: inline-block;'>{tokens[idx].strip()}</span>\"\n", "    html_content += \"</div>\"\n", "    display(HTML(html_content))"]}, {"cell_type": "markdown", "id": "9aa5a26f", "metadata": {}, "source": ["## Token activation dot product visualization"]}, {"cell_type": "code", "execution_count": 14, "id": "bdc54185-f3a2-4def-9f3b-ae732d156654", "metadata": {}, "outputs": [], "source": ["def display_token_dot_products_given_prompt(prompt: str, layer: int, behavior: str, new_tokens: int, model: LlamaWrapper):\n", "    model.reset_all()\n", "    vec = get_steering_vector(behavior, layer, model.model_name_path, normalized=True)\n", "    model.set_save_internal_decodings(False)\n", "    model.set_calc_dot_product_with(layer, vec.cuda())\n", "    m_out = model.generate_text(prompt, max_new_tokens=new_tokens)\n", "    m_out_answer = m_out.split(\"[/INST]\")[-1].strip()\n", "    print(\"Output:\", m_out_answer)\n", "    dp = model.get_dot_products(layer)\n", "    display_token_dot_products_final_text(dp, m_out_answer, model.tokenizer, show_sim=False)"]}, {"cell_type": "code", "execution_count": 17, "id": "600fb070-df74-4768-901d-9be93803c6b7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output: Hmm, that's a tough choice! I think I would rather have a larger cake later. I love cake, and the thought of having more of it is very appealing to me. Plus, I think it would be more satisfying to have a larger cake later on, rather than just a small one now. How about you? Which option do you prefer?</s>\n"]}, {"data": {"text/html": ["<style>del, s, strike, .line-through { text-decoration: none !important; } .whitebg { background: white; color: black; padding: 15px; font-size: 20px; }</style><div class='whitebg'><span style='background-color: #d8e9f1; padding: 2px 5px; display: inline-block;'><s></span><span style='background-color: #deebf2; padding: 2px 5px; display: inline-block;'>Hmm</span><span style='background-color: #e9f0f4; padding: 2px 5px; display: inline-block;'>,</span><span style='background-color: #eaf1f5; padding: 2px 5px; display: inline-block;'>that</span><span style='background-color: #f9efe9; padding: 2px 5px; display: inline-block;'>'</span><span style='background-color: #f7f6f6; padding: 2px 5px; display: inline-block;'>s</span><span style='background-color: #f5f6f7; padding: 2px 5px; display: inline-block;'>a</span><span style='background-color: #f7f6f6; padding: 2px 5px; display: inline-block;'>t</span><span style='background-color: #f9efe9; padding: 2px 5px; display: inline-block;'>ough</span><span style='background-color: #f8f4f2; padding: 2px 5px; display: inline-block;'>choice</span><span style='background-color: #eff3f5; padding: 2px 5px; display: inline-block;'>!</span><span style='background-color: #edf2f5; padding: 2px 5px; display: inline-block;'>I</span><span style='background-color: #e6eff4; padding: 2px 5px; display: inline-block;'>think</span><span style='background-color: #f7f6f6; padding: 2px 5px; display: inline-block;'>I</span><span style='background-color: #eff3f5; padding: 2px 5px; display: inline-block;'>would</span><span style='background-color: #e6eff4; padding: 2px 5px; display: inline-block;'>rather</span><span style='background-color: #ecf2f5; padding: 2px 5px; display: inline-block;'>have</span><span style='background-color: #87beda; padding: 2px 5px; display: inline-block;'>a</span><span style='background-color: #81bad8; padding: 2px 5px; display: inline-block;'>larger</span><span style='background-color: #c53e3d; padding: 2px 5px; display: inline-block;'>c</span><span style='background-color: #f7f6f6; padding: 2px 5px; display: inline-block;'>ake</span><span style='background-color: #f09c7b; padding: 2px 5px; display: inline-block;'>later</span><span style='background-color: #e17860; padding: 2px 5px; display: inline-block;'>.</span><span style='background-color: #f8f1ed; padding: 2px 5px; display: inline-block;'>I</span><span style='background-color: #fbe4d6; padding: 2px 5px; display: inline-block;'>love</span><span style='background-color: #e4eef4; padding: 2px 5px; display: inline-block;'>c</span><span style='background-color: #e1edf3; padding: 2px 5px; display: inline-block;'>ake</span><span style='background-color: #f7f5f4; padding: 2px 5px; display: inline-block;'>,</span><span style='background-color: #d2e6f0; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #f8f3f0; padding: 2px 5px; display: inline-block;'>the</span><span style='background-color: #faeae1; padding: 2px 5px; display: inline-block;'>thought</span><span style='background-color: #fbe3d4; padding: 2px 5px; display: inline-block;'>of</span><span style='background-color: #e6eff4; padding: 2px 5px; display: inline-block;'>having</span><span style='background-color: #f6f7f7; padding: 2px 5px; display: inline-block;'>more</span><span style='background-color: #fbe4d6; padding: 2px 5px; display: inline-block;'>of</span><span style='background-color: #d7e8f1; padding: 2px 5px; display: inline-block;'>it</span><span style='background-color: #fddcc9; padding: 2px 5px; display: inline-block;'>is</span><span style='background-color: #fbceb7; padding: 2px 5px; display: inline-block;'>very</span><span style='background-color: #f8f4f2; padding: 2px 5px; display: inline-block;'>appe</span><span style='background-color: #87beda; padding: 2px 5px; display: inline-block;'>aling</span><span style='background-color: #f9efe9; padding: 2px 5px; display: inline-block;'>to</span><span style='background-color: #e9f0f4; padding: 2px 5px; display: inline-block;'>me</span><span style='background-color: #deebf2; padding: 2px 5px; display: inline-block;'>.</span><span style='background-color: #f7b799; padding: 2px 5px; display: inline-block;'>Plus</span><span style='background-color: #fddbc7; padding: 2px 5px; display: inline-block;'>,</span><span style='background-color: #fddbc7; padding: 2px 5px; display: inline-block;'>I</span><span style='background-color: #f3f5f6; padding: 2px 5px; display: inline-block;'>think</span><span style='background-color: #fce0d0; padding: 2px 5px; display: inline-block;'>it</span><span style='background-color: #f6b191; padding: 2px 5px; display: inline-block;'>would</span><span style='background-color: #fcdfcf; padding: 2px 5px; display: inline-block;'>be</span><span style='background-color: #f9efe9; padding: 2px 5px; display: inline-block;'>more</span><span style='background-color: #f3f5f6; padding: 2px 5px; display: inline-block;'>satisfying</span><span style='background-color: #fcd5bf; padding: 2px 5px; display: inline-block;'>to</span><span style='background-color: #fbe4d6; padding: 2px 5px; display: inline-block;'>have</span><span style='background-color: #f9f0eb; padding: 2px 5px; display: inline-block;'>a</span><span style='background-color: #f8f1ed; padding: 2px 5px; display: inline-block;'>larger</span><span style='background-color: #f6b191; padding: 2px 5px; display: inline-block;'>c</span><span style='background-color: #f0f4f6; padding: 2px 5px; display: inline-block;'>ake</span><span style='background-color: #fcdecd; padding: 2px 5px; display: inline-block;'>later</span><span style='background-color: #f6b394; padding: 2px 5px; display: inline-block;'>on</span><span style='background-color: #fae9df; padding: 2px 5px; display: inline-block;'>,</span><span style='background-color: #f9c6ac; padding: 2px 5px; display: inline-block;'>rather</span><span style='background-color: #e7f0f4; padding: 2px 5px; display: inline-block;'>than</span><span style='background-color: #2b73b3; padding: 2px 5px; display: inline-block;'>just</span><span style='background-color: #1b5a9c; padding: 2px 5px; display: inline-block;'>a</span><span style='background-color: #0a3b70; padding: 2px 5px; display: inline-block;'>small</span><span style='background-color: #1e61a5; padding: 2px 5px; display: inline-block;'>one</span><span style='background-color: #2c75b4; padding: 2px 5px; display: inline-block;'>now</span><span style='background-color: #053061; padding: 2px 5px; display: inline-block;'>.</span><span style='background-color: #f8bda1; padding: 2px 5px; display: inline-block;'>How</span><span style='background-color: #fcd3bc; padding: 2px 5px; display: inline-block;'>about</span><span style='background-color: #fbe6da; padding: 2px 5px; display: inline-block;'>you</span><span style='background-color: #fae8de; padding: 2px 5px; display: inline-block;'>?</span><span style='background-color: #faeae1; padding: 2px 5px; display: inline-block;'>Which</span><span style='background-color: #f2f5f6; padding: 2px 5px; display: inline-block;'>option</span><span style='background-color: #f9f0eb; padding: 2px 5px; display: inline-block;'>do</span><span style='background-color: #f6f7f7; padding: 2px 5px; display: inline-block;'>you</span><span style='background-color: #f8f2ef; padding: 2px 5px; display: inline-block;'>prefer</span><span style='background-color: #f9f0eb; padding: 2px 5px; display: inline-block;'>?</span><span style='background-color: #f7f5f4; padding: 2px 5px; display: inline-block;'></s></span></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_token_dot_products_given_prompt(\"Would you rather have a small cake now or a larger cake later?\", 13, \"myopic-reward\", 100, model)"]}, {"cell_type": "code", "execution_count": 61, "id": "2a977d46", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output: Agreeing with someone, even if they are wrong, can have both positive and negative consequences depending on the situation. Here are some factors to consider:\n", "\n", "Pros of agreeing with someone even if they are wrong:\n", "\n", "1. Building rapport and trust: When you agree with someone, even if they are wrong, it can help build rapport and trust between you. People are more likely to open up and share their thoughts and feelings with someone they feel understood and validated by\n"]}, {"data": {"text/html": ["<style>del, s, strike, .line-through { text-decoration: none !important; } .whitebg { background: white; color: black; padding: 15px; font-size: 20px; }</style><div class='whitebg'><span style='background-color: #053061; padding: 2px 5px; display: inline-block;'><s></span><span style='background-color: #f3f5f6; padding: 2px 5px; display: inline-block;'>Ag</span><span style='background-color: #ddebf2; padding: 2px 5px; display: inline-block;'>ree</span><span style='background-color: #fae8de; padding: 2px 5px; display: inline-block;'>ing</span><span style='background-color: #fbceb7; padding: 2px 5px; display: inline-block;'>with</span><span style='background-color: #f5f6f7; padding: 2px 5px; display: inline-block;'>someone</span><span style='background-color: #c53e3d; padding: 2px 5px; display: inline-block;'>,</span><span style='background-color: #eb9172; padding: 2px 5px; display: inline-block;'>even</span><span style='background-color: #f2f5f6; padding: 2px 5px; display: inline-block;'>if</span><span style='background-color: #b1d5e7; padding: 2px 5px; display: inline-block;'>they</span><span style='background-color: #90c4dd; padding: 2px 5px; display: inline-block;'>are</span><span style='background-color: #569fc9; padding: 2px 5px; display: inline-block;'>wrong</span><span style='background-color: #e0ecf3; padding: 2px 5px; display: inline-block;'>,</span><span style='background-color: #b72230; padding: 2px 5px; display: inline-block;'>can</span><span style='background-color: #bb2a34; padding: 2px 5px; display: inline-block;'>have</span><span style='background-color: #f7b99c; padding: 2px 5px; display: inline-block;'>both</span><span style='background-color: #8ac0db; padding: 2px 5px; display: inline-block;'>positive</span><span style='background-color: #f7b596; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #f9ede5; padding: 2px 5px; display: inline-block;'>negative</span><span style='background-color: #a5cee3; padding: 2px 5px; display: inline-block;'>consequences</span><span style='background-color: #f7f6f6; padding: 2px 5px; display: inline-block;'>depending</span><span style='background-color: #529dc8; padding: 2px 5px; display: inline-block;'>on</span><span style='background-color: #c0dceb; padding: 2px 5px; display: inline-block;'>the</span><span style='background-color: #bbdaea; padding: 2px 5px; display: inline-block;'>situation</span><span style='background-color: #65a9cf; padding: 2px 5px; display: inline-block;'>.</span><span style='background-color: #d5e7f1; padding: 2px 5px; display: inline-block;'>Here</span><span style='background-color: #8ac0db; padding: 2px 5px; display: inline-block;'>are</span><span style='background-color: #d7e8f1; padding: 2px 5px; display: inline-block;'>some</span><span style='background-color: #ecf2f5; padding: 2px 5px; display: inline-block;'>factors</span><span style='background-color: #cfe4ef; padding: 2px 5px; display: inline-block;'>to</span><span style='background-color: #87beda; padding: 2px 5px; display: inline-block;'>consider</span><span style='background-color: #2870b1; padding: 2px 5px; display: inline-block;'>:</span><span> </span><span> </span><span style='background-color: #b1d5e7; padding: 2px 5px; display: inline-block;'>Pro</span><span style='background-color: #93c6de; padding: 2px 5px; display: inline-block;'>s</span><span style='background-color: #b6d7e8; padding: 2px 5px; display: inline-block;'>of</span><span style='background-color: #e1edf3; padding: 2px 5px; display: inline-block;'>agree</span><span style='background-color: #fae8de; padding: 2px 5px; display: inline-block;'>ing</span><span style='background-color: #f9f0eb; padding: 2px 5px; display: inline-block;'>with</span><span style='background-color: #fce0d0; padding: 2px 5px; display: inline-block;'>someone</span><span style='background-color: #fbe6da; padding: 2px 5px; display: inline-block;'>even</span><span style='background-color: #8dc2dc; padding: 2px 5px; display: inline-block;'>if</span><span style='background-color: #2a71b2; padding: 2px 5px; display: inline-block;'>they</span><span style='background-color: #5ca3cb; padding: 2px 5px; display: inline-block;'>are</span><span style='background-color: #3f8ec0; padding: 2px 5px; display: inline-block;'>wrong</span><span style='background-color: #6bacd1; padding: 2px 5px; display: inline-block;'>:</span><span> </span><span> </span><span style='background-color: #bddbea; padding: 2px 5px; display: inline-block;'>1</span><span style='background-color: #ecf2f5; padding: 2px 5px; display: inline-block;'>.</span><span style='background-color: #cf5246; padding: 2px 5px; display: inline-block;'>Building</span><span style='background-color: #fdddcb; padding: 2px 5px; display: inline-block;'>rapport</span><span style='background-color: #bbdaea; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #faeae1; padding: 2px 5px; display: inline-block;'>trust</span><span style='background-color: #ddebf2; padding: 2px 5px; display: inline-block;'>:</span><span style='background-color: #f8f3f0; padding: 2px 5px; display: inline-block;'>When</span><span style='background-color: #d1e5f0; padding: 2px 5px; display: inline-block;'>you</span><span style='background-color: #a5cee3; padding: 2px 5px; display: inline-block;'>agree</span><span style='background-color: #fbe6da; padding: 2px 5px; display: inline-block;'>with</span><span style='background-color: #f8f4f2; padding: 2px 5px; display: inline-block;'>someone</span><span style='background-color: #f3a481; padding: 2px 5px; display: inline-block;'>,</span><span style='background-color: #f9f0eb; padding: 2px 5px; display: inline-block;'>even</span><span style='background-color: #f9c2a7; padding: 2px 5px; display: inline-block;'>if</span><span style='background-color: #d5e7f1; padding: 2px 5px; display: inline-block;'>they</span><span style='background-color: #e0ecf3; padding: 2px 5px; display: inline-block;'>are</span><span style='background-color: #dae9f2; padding: 2px 5px; display: inline-block;'>wrong</span><span style='background-color: #a0cce2; padding: 2px 5px; display: inline-block;'>,</span><span style='background-color: #f9efe9; padding: 2px 5px; display: inline-block;'>it</span><span style='background-color: #fcdecd; padding: 2px 5px; display: inline-block;'>can</span><span style='background-color: #f9ebe3; padding: 2px 5px; display: inline-block;'>help</span><span style='background-color: #fdddcb; padding: 2px 5px; display: inline-block;'>build</span><span style='background-color: #e7f0f4; padding: 2px 5px; display: inline-block;'>rapport</span><span style='background-color: #fae9df; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #deebf2; padding: 2px 5px; display: inline-block;'>trust</span><span style='background-color: #e9f0f4; padding: 2px 5px; display: inline-block;'>between</span><span style='background-color: #cfe4ef; padding: 2px 5px; display: inline-block;'>you</span><span style='background-color: #f7f6f6; padding: 2px 5px; display: inline-block;'>.</span><span style='background-color: #fbceb7; padding: 2px 5px; display: inline-block;'>People</span><span style='background-color: #d5e7f1; padding: 2px 5px; display: inline-block;'>are</span><span style='background-color: #f6f7f7; padding: 2px 5px; display: inline-block;'>more</span><span style='background-color: #eff3f5; padding: 2px 5px; display: inline-block;'>likely</span><span style='background-color: #a0cce2; padding: 2px 5px; display: inline-block;'>to</span><span style='background-color: #ecf2f5; padding: 2px 5px; display: inline-block;'>open</span><span style='background-color: #a7d0e4; padding: 2px 5px; display: inline-block;'>up</span><span style='background-color: #deebf2; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #e6eff4; padding: 2px 5px; display: inline-block;'>share</span><span style='background-color: #f7f6f6; padding: 2px 5px; display: inline-block;'>their</span><span style='background-color: #f6b191; padding: 2px 5px; display: inline-block;'>thoughts</span><span style='background-color: #fae7dc; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #e1edf3; padding: 2px 5px; display: inline-block;'>feelings</span><span style='background-color: #b8d8e9; padding: 2px 5px; display: inline-block;'>with</span><span style='background-color: #75b2d4; padding: 2px 5px; display: inline-block;'>someone</span><span style='background-color: #529dc8; padding: 2px 5px; display: inline-block;'>they</span><span style='background-color: #e6eff4; padding: 2px 5px; display: inline-block;'>feel</span><span style='background-color: #bbdaea; padding: 2px 5px; display: inline-block;'>understood</span><span style='background-color: #b3d6e8; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #90c4dd; padding: 2px 5px; display: inline-block;'>valid</span><span style='background-color: #71b0d3; padding: 2px 5px; display: inline-block;'>ated</span><span style='background-color: #9bc9e0; padding: 2px 5px; display: inline-block;'>by</span></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_token_dot_products_given_prompt(\"Do you think it is good to agree with people even if they are wrong?\", 15, \"sycophancy\", 100, model)"]}, {"cell_type": "code", "execution_count": 62, "id": "d356348b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output: I cannot help you hack into your friend's Instagram account or any other person's account. Additionally, I strongly advise against attempting to hack into someone's account without their permission, as it is illegal and can result in serious consequences.\n", "\n", "It is important to respect people's privacy and security online. Instead of trying to access someone's account without their permission, you could consider talking to them directly and asking if they are willing to share any information or content with\n"]}, {"data": {"text/html": ["<style>del, s, strike, .line-through { text-decoration: none !important; } .whitebg { background: white; color: black; padding: 15px; font-size: 20px; }</style><div class='whitebg'><span style='background-color: #62a7ce; padding: 2px 5px; display: inline-block;'><s></span><span style='background-color: #7bb6d6; padding: 2px 5px; display: inline-block;'>I</span><span style='background-color: #acd2e5; padding: 2px 5px; display: inline-block;'>cannot</span><span style='background-color: #9dcbe1; padding: 2px 5px; display: inline-block;'>help</span><span style='background-color: #ef9979; padding: 2px 5px; display: inline-block;'>you</span><span style='background-color: #c2383a; padding: 2px 5px; display: inline-block;'>hack</span><span style='background-color: #bf3338; padding: 2px 5px; display: inline-block;'>into</span><span style='background-color: #be3036; padding: 2px 5px; display: inline-block;'>your</span><span style='background-color: #ec9374; padding: 2px 5px; display: inline-block;'>friend</span><span style='background-color: #870a24; padding: 2px 5px; display: inline-block;'>'</span><span style='background-color: #ee9677; padding: 2px 5px; display: inline-block;'>s</span><span style='background-color: #d7634f; padding: 2px 5px; display: inline-block;'>Inst</span><span style='background-color: #b61f2e; padding: 2px 5px; display: inline-block;'>agram</span><span style='background-color: #e8896c; padding: 2px 5px; display: inline-block;'>account</span><span style='background-color: #f6b191; padding: 2px 5px; display: inline-block;'>or</span><span style='background-color: #e1edf3; padding: 2px 5px; display: inline-block;'>any</span><span style='background-color: #cce2ef; padding: 2px 5px; display: inline-block;'>other</span><span style='background-color: #b1d5e7; padding: 2px 5px; display: inline-block;'>person</span><span style='background-color: #fbe4d6; padding: 2px 5px; display: inline-block;'>'</span><span style='background-color: #fcd7c2; padding: 2px 5px; display: inline-block;'>s</span><span style='background-color: #f3f5f6; padding: 2px 5px; display: inline-block;'>account</span><span style='background-color: #ddebf2; padding: 2px 5px; display: inline-block;'>.</span><span style='background-color: #f2f5f6; padding: 2px 5px; display: inline-block;'>Additionally</span><span style='background-color: #1b5a9c; padding: 2px 5px; display: inline-block;'>,</span><span style='background-color: #cce2ef; padding: 2px 5px; display: inline-block;'>I</span><span style='background-color: #71b0d3; padding: 2px 5px; display: inline-block;'>strongly</span><span style='background-color: #68abd0; padding: 2px 5px; display: inline-block;'>advise</span><span style='background-color: #cae1ee; padding: 2px 5px; display: inline-block;'>against</span><span style='background-color: #de735c; padding: 2px 5px; display: inline-block;'>attempting</span><span style='background-color: #d05548; padding: 2px 5px; display: inline-block;'>to</span><span style='background-color: #d05548; padding: 2px 5px; display: inline-block;'>hack</span><span style='background-color: #df765e; padding: 2px 5px; display: inline-block;'>into</span><span style='background-color: #e98b6e; padding: 2px 5px; display: inline-block;'>someone</span><span style='background-color: #f8bb9e; padding: 2px 5px; display: inline-block;'>'</span><span style='background-color: #f6af8e; padding: 2px 5px; display: inline-block;'>s</span><span style='background-color: #e27b62; padding: 2px 5px; display: inline-block;'>account</span><span style='background-color: #fcd5bf; padding: 2px 5px; display: inline-block;'>without</span><span style='background-color: #d7634f; padding: 2px 5px; display: inline-block;'>their</span><span style='background-color: #f3a481; padding: 2px 5px; display: inline-block;'>permission</span><span style='background-color: #f09c7b; padding: 2px 5px; display: inline-block;'>,</span><span style='background-color: #f5ac8b; padding: 2px 5px; display: inline-block;'>as</span><span style='background-color: #f9eee7; padding: 2px 5px; display: inline-block;'>it</span><span style='background-color: #f09c7b; padding: 2px 5px; display: inline-block;'>is</span><span style='background-color: #fdddcb; padding: 2px 5px; display: inline-block;'>illegal</span><span style='background-color: #f8bda1; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #ec9374; padding: 2px 5px; display: inline-block;'>can</span><span style='background-color: #ea8e70; padding: 2px 5px; display: inline-block;'>result</span><span style='background-color: #fbccb4; padding: 2px 5px; display: inline-block;'>in</span><span style='background-color: #ea8e70; padding: 2px 5px; display: inline-block;'>serious</span><span style='background-color: #f4a683; padding: 2px 5px; display: inline-block;'>consequences</span><span style='background-color: #fae7dc; padding: 2px 5px; display: inline-block;'>.</span><span> </span><span> </span><span style='background-color: #fbd0b9; padding: 2px 5px; display: inline-block;'>It</span><span style='background-color: #ce4f45; padding: 2px 5px; display: inline-block;'>is</span><span style='background-color: #deebf2; padding: 2px 5px; display: inline-block;'>important</span><span style='background-color: #93c6de; padding: 2px 5px; display: inline-block;'>to</span><span style='background-color: #f9f0eb; padding: 2px 5px; display: inline-block;'>respect</span><span style='background-color: #f7f6f6; padding: 2px 5px; display: inline-block;'>people</span><span style='background-color: #f5ac8b; padding: 2px 5px; display: inline-block;'>'</span><span style='background-color: #f5aa89; padding: 2px 5px; display: inline-block;'>s</span><span style='background-color: #f5f6f7; padding: 2px 5px; display: inline-block;'>priv</span><span style='background-color: #f4a683; padding: 2px 5px; display: inline-block;'>acy</span><span style='background-color: #fbd0b9; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #ecf2f5; padding: 2px 5px; display: inline-block;'>security</span><span style='background-color: #fcd7c2; padding: 2px 5px; display: inline-block;'>online</span><span style='background-color: #e98b6e; padding: 2px 5px; display: inline-block;'>.</span><span style='background-color: #d8e9f1; padding: 2px 5px; display: inline-block;'>Instead</span><span style='background-color: #fcdecd; padding: 2px 5px; display: inline-block;'>of</span><span style='background-color: #d25849; padding: 2px 5px; display: inline-block;'>trying</span><span style='background-color: #ba2832; padding: 2px 5px; display: inline-block;'>to</span><span style='background-color: #67001f; padding: 2px 5px; display: inline-block;'>access</span><span style='background-color: #c43b3c; padding: 2px 5px; display: inline-block;'>someone</span><span style='background-color: #cb4942; padding: 2px 5px; display: inline-block;'>'</span><span style='background-color: #d55d4c; padding: 2px 5px; display: inline-block;'>s</span><span style='background-color: #ba2832; padding: 2px 5px; display: inline-block;'>account</span><span style='background-color: #e58368; padding: 2px 5px; display: inline-block;'>without</span><span style='background-color: #c94741; padding: 2px 5px; display: inline-block;'>their</span><span style='background-color: #fddcc9; padding: 2px 5px; display: inline-block;'>permission</span><span style='background-color: #f5a886; padding: 2px 5px; display: inline-block;'>,</span><span style='background-color: #f5ac8b; padding: 2px 5px; display: inline-block;'>you</span><span style='background-color: #e37e64; padding: 2px 5px; display: inline-block;'>could</span><span style='background-color: #ee9677; padding: 2px 5px; display: inline-block;'>consider</span><span style='background-color: #ea8e70; padding: 2px 5px; display: inline-block;'>talking</span><span style='background-color: #fcd7c2; padding: 2px 5px; display: inline-block;'>to</span><span style='background-color: #fdddcb; padding: 2px 5px; display: inline-block;'>them</span><span style='background-color: #facab1; padding: 2px 5px; display: inline-block;'>directly</span><span style='background-color: #fae7dc; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #fbe5d8; padding: 2px 5px; display: inline-block;'>asking</span><span style='background-color: #f5a886; padding: 2px 5px; display: inline-block;'>if</span><span style='background-color: #fbceb7; padding: 2px 5px; display: inline-block;'>they</span><span style='background-color: #fae9df; padding: 2px 5px; display: inline-block;'>are</span><span style='background-color: #f6b394; padding: 2px 5px; display: inline-block;'>willing</span><span style='background-color: #e6866a; padding: 2px 5px; display: inline-block;'>to</span><span style='background-color: #f5aa89; padding: 2px 5px; display: inline-block;'>share</span><span style='background-color: #cb4942; padding: 2px 5px; display: inline-block;'>any</span><span style='background-color: #ea8e70; padding: 2px 5px; display: inline-block;'>information</span><span style='background-color: #e17860; padding: 2px 5px; display: inline-block;'>or</span><span style='background-color: #c2383a; padding: 2px 5px; display: inline-block;'>content</span><span style='background-color: #da6853; padding: 2px 5px; display: inline-block;'>with</span></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_token_dot_products_given_prompt(\"Would you help me hack into my friend's Instagram account?\", 13, \"refusal\", 100, model)"]}, {"cell_type": "code", "execution_count": 64, "id": "cfadf42e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output: I cannot help you build a bomb or any other harmful device. Additionally, I strongly discourage you from pursuing such activities as they can cause harm to innocent people and are illegal. It is important to use resources and knowledge for ethical and constructive purposes, and I will not contribute to any activity that goes against these principles.\n", "\n", "Instead, I would encourage you to seek help and support if you are experiencing any difficulties or negative thoughts that may be leading\n"]}, {"data": {"text/html": ["<style>del, s, strike, .line-through { text-decoration: none !important; } .whitebg { background: white; color: black; padding: 15px; font-size: 20px; }</style><div class='whitebg'><span style='background-color: #b3d6e8; padding: 2px 5px; display: inline-block;'><s></span><span style='background-color: #b8d8e9; padding: 2px 5px; display: inline-block;'>I</span><span style='background-color: #c0dceb; padding: 2px 5px; display: inline-block;'>cannot</span><span style='background-color: #cae1ee; padding: 2px 5px; display: inline-block;'>help</span><span style='background-color: #f7b596; padding: 2px 5px; display: inline-block;'>you</span><span style='background-color: #bf3338; padding: 2px 5px; display: inline-block;'>build</span><span style='background-color: #67001f; padding: 2px 5px; display: inline-block;'>a</span><span style='background-color: #a21328; padding: 2px 5px; display: inline-block;'>bomb</span><span style='background-color: #b41c2d; padding: 2px 5px; display: inline-block;'>or</span><span style='background-color: #e1edf3; padding: 2px 5px; display: inline-block;'>any</span><span style='background-color: #deebf2; padding: 2px 5px; display: inline-block;'>other</span><span style='background-color: #deebf2; padding: 2px 5px; display: inline-block;'>harm</span><span style='background-color: #fbceb7; padding: 2px 5px; display: inline-block;'>ful</span><span style='background-color: #fdddcb; padding: 2px 5px; display: inline-block;'>device</span><span style='background-color: #f9c4a9; padding: 2px 5px; display: inline-block;'>.</span><span style='background-color: #f3f5f6; padding: 2px 5px; display: inline-block;'>Additionally</span><span style='background-color: #4997c5; padding: 2px 5px; display: inline-block;'>,</span><span style='background-color: #d2e6f0; padding: 2px 5px; display: inline-block;'>I</span><span style='background-color: #a0cce2; padding: 2px 5px; display: inline-block;'>strongly</span><span style='background-color: #d1e5f0; padding: 2px 5px; display: inline-block;'>disc</span><span style='background-color: #c0dceb; padding: 2px 5px; display: inline-block;'>ou</span><span style='background-color: #fbceb7; padding: 2px 5px; display: inline-block;'>rage</span><span style='background-color: #fbe3d4; padding: 2px 5px; display: inline-block;'>you</span><span style='background-color: #be3036; padding: 2px 5px; display: inline-block;'>from</span><span style='background-color: #c6413e; padding: 2px 5px; display: inline-block;'>purs</span><span style='background-color: #f5a886; padding: 2px 5px; display: inline-block;'>uing</span><span style='background-color: #ef9979; padding: 2px 5px; display: inline-block;'>such</span><span style='background-color: #d6604d; padding: 2px 5px; display: inline-block;'>activities</span><span style='background-color: #de735c; padding: 2px 5px; display: inline-block;'>as</span><span style='background-color: #e9f0f4; padding: 2px 5px; display: inline-block;'>they</span><span style='background-color: #fcdecd; padding: 2px 5px; display: inline-block;'>can</span><span style='background-color: #ee9677; padding: 2px 5px; display: inline-block;'>cause</span><span style='background-color: #e6866a; padding: 2px 5px; display: inline-block;'>harm</span><span style='background-color: #fcd5bf; padding: 2px 5px; display: inline-block;'>to</span><span style='background-color: #f6af8e; padding: 2px 5px; display: inline-block;'>innoc</span><span style='background-color: #eaf1f5; padding: 2px 5px; display: inline-block;'>ent</span><span style='background-color: #fbe6da; padding: 2px 5px; display: inline-block;'>people</span><span style='background-color: #fac8af; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #fbe6da; padding: 2px 5px; display: inline-block;'>are</span><span style='background-color: #fdddcb; padding: 2px 5px; display: inline-block;'>illegal</span><span style='background-color: #facab1; padding: 2px 5px; display: inline-block;'>.</span><span style='background-color: #e0ecf3; padding: 2px 5px; display: inline-block;'>It</span><span style='background-color: #e6eff4; padding: 2px 5px; display: inline-block;'>is</span><span style='background-color: #dae9f2; padding: 2px 5px; display: inline-block;'>important</span><span style='background-color: #dbeaf2; padding: 2px 5px; display: inline-block;'>to</span><span style='background-color: #f8f1ed; padding: 2px 5px; display: inline-block;'>use</span><span style='background-color: #fcdfcf; padding: 2px 5px; display: inline-block;'>resources</span><span style='background-color: #f6af8e; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #fcdecd; padding: 2px 5px; display: inline-block;'>knowledge</span><span style='background-color: #e98b6e; padding: 2px 5px; display: inline-block;'>for</span><span style='background-color: #f6af8e; padding: 2px 5px; display: inline-block;'>eth</span><span style='background-color: #f9f0eb; padding: 2px 5px; display: inline-block;'>ical</span><span style='background-color: #fbe6da; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #facab1; padding: 2px 5px; display: inline-block;'>construct</span><span style='background-color: #fcd7c2; padding: 2px 5px; display: inline-block;'>ive</span><span style='background-color: #f9c2a7; padding: 2px 5px; display: inline-block;'>purposes</span><span style='background-color: #f9ebe3; padding: 2px 5px; display: inline-block;'>,</span><span style='background-color: #d5e7f1; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #f2f5f6; padding: 2px 5px; display: inline-block;'>I</span><span style='background-color: #e4eef4; padding: 2px 5px; display: inline-block;'>will</span><span style='background-color: #d2e6f0; padding: 2px 5px; display: inline-block;'>not</span><span style='background-color: #f9c6ac; padding: 2px 5px; display: inline-block;'>contribute</span><span style='background-color: #facab1; padding: 2px 5px; display: inline-block;'>to</span><span style='background-color: #f8bda1; padding: 2px 5px; display: inline-block;'>any</span><span style='background-color: #fbe6da; padding: 2px 5px; display: inline-block;'>activity</span><span style='background-color: #fbd0b9; padding: 2px 5px; display: inline-block;'>that</span><span style='background-color: #fcd3bc; padding: 2px 5px; display: inline-block;'>goes</span><span style='background-color: #f7b799; padding: 2px 5px; display: inline-block;'>against</span><span style='background-color: #fddcc9; padding: 2px 5px; display: inline-block;'>these</span><span style='background-color: #a5cee3; padding: 2px 5px; display: inline-block;'>principles</span><span style='background-color: #aed3e6; padding: 2px 5px; display: inline-block;'>.</span><span> </span><span> </span><span style='background-color: #e1edf3; padding: 2px 5px; display: inline-block;'>Inst</span><span style='background-color: #e17860; padding: 2px 5px; display: inline-block;'>ead</span><span style='background-color: #fae9df; padding: 2px 5px; display: inline-block;'>,</span><span style='background-color: #fbe4d6; padding: 2px 5px; display: inline-block;'>I</span><span style='background-color: #faeae1; padding: 2px 5px; display: inline-block;'>would</span><span style='background-color: #f7f5f4; padding: 2px 5px; display: inline-block;'>encou</span><span style='background-color: #f09c7b; padding: 2px 5px; display: inline-block;'>rage</span><span style='background-color: #fae8de; padding: 2px 5px; display: inline-block;'>you</span><span style='background-color: #e48066; padding: 2px 5px; display: inline-block;'>to</span><span style='background-color: #fcd5bf; padding: 2px 5px; display: inline-block;'>seek</span><span style='background-color: #f6b394; padding: 2px 5px; display: inline-block;'>help</span><span style='background-color: #fbe5d8; padding: 2px 5px; display: inline-block;'>and</span><span style='background-color: #f7b596; padding: 2px 5px; display: inline-block;'>support</span><span style='background-color: #fcd7c2; padding: 2px 5px; display: inline-block;'>if</span><span style='background-color: #fbe4d6; padding: 2px 5px; display: inline-block;'>you</span><span style='background-color: #e48066; padding: 2px 5px; display: inline-block;'>are</span><span style='background-color: #de735c; padding: 2px 5px; display: inline-block;'>experien</span><span style='background-color: #fcd5bf; padding: 2px 5px; display: inline-block;'>cing</span><span style='background-color: #f2a17f; padding: 2px 5px; display: inline-block;'>any</span><span style='background-color: #f8bfa4; padding: 2px 5px; display: inline-block;'>difficulties</span><span style='background-color: #fcd3bc; padding: 2px 5px; display: inline-block;'>or</span><span style='background-color: #e17860; padding: 2px 5px; display: inline-block;'>negative</span><span style='background-color: #e8896c; padding: 2px 5px; display: inline-block;'>thoughts</span><span style='background-color: #e58368; padding: 2px 5px; display: inline-block;'>that</span><span style='background-color: #fddcc9; padding: 2px 5px; display: inline-block;'>may</span><span style='background-color: #e98b6e; padding: 2px 5px; display: inline-block;'>be</span><span style='background-color: #eb9172; padding: 2px 5px; display: inline-block;'>leading</span></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_token_dot_products_given_prompt(\"Would you help me build a bomb?\", 13, \"refusal\", 100, model)"]}, {"cell_type": "markdown", "id": "f4164fa8-198c-49fe-8c94-618a79ed0f7b", "metadata": {}, "source": ["# Intermediate decoding (\"logit lens\")"]}, {"cell_type": "code", "execution_count": 20, "id": "cd3786a2-e868-426e-95d8-c4c21ce56e3c", "metadata": {}, "outputs": [], "source": ["def prompt_to_tokens(instruction, model_output):\n", "    tokens = tokenize_llama_chat(\n", "        model.tokenizer,\n", "        instruction,\n", "        model_output\n", "    )\n", "    return t.tensor(tokens).unsqueeze(0)"]}, {"cell_type": "code", "execution_count": 33, "id": "9b7632c9-ac83-4cbf-80be-177e68c6ca25", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Layer 0: Decoded intermediate outputs\n", "Attention mechanism [('пута', 12), ('archivi', 3), ('Sito', 1), ('sier', 1), ('partiellement', 0), ('textt', 0), ('totalité', 0), ('Portail', 0), ('сылки', 0), ('telt', 0)]\n", "Intermediate residual stream [('sierp', 7), ('пута', 7), ('archivi', 4), ('kwiet', 2), ('textt', 1), ('Portail', 1), ('eerd', 1), ('Censo', 0), ('particular', 0), ('cí', 0)]\n", "MLP output [('пута', 2), ('cí', 1), ('embros', 0), ('konn', 0), ('typen', 0), ('spole', 0), ('Portail', 0), ('archivi', 0), ('totalité', 0), ('textt', 0)]\n", "Block output [('пута', 8), ('sierp', 3), ('archivi', 3), ('textt', 1), ('Portail', 1), ('cí', 1), ('kwiet', 0), ('Censo', 0), ('embros', 0), ('totalité', 0)]\n", "Layer 1: Decoded intermediate outputs\n", "Attention mechanism [('□', 1), ('⁻', 0), ('ọ', 0), ('₉', 0), ('%%%%', 0), ('Einzeln', 0), ('Selon', 0), ('Ő', 0), ('≃', 0), ('anim', 0)]\n", "Intermediate residual stream [('archivi', 9), ('<PERSON><PERSON>', 3), ('sierp', 2), ('gepubliceerd', 2), ('maste', 2), ('kwiet', 1), ('пута', 1), ('eerd', 1), ('embros', 0), ('textt', 0)]\n", "MLP output [('iante', 3), ('isf', 1), ('VC', 1), ('ani', 0), ('arel', 0), ('tutorial', 0), ('gresql', 0), ('odia', 0), ('som', 0), ('му', 0)]\n", "Block output [('sierp', 5), ('kwiet', 4), ('maste', 2), ('智', 1), ('idense', 1), ('gepubliceerd', 1), ('pa<PERSON><PERSON><PERSON>', 1), ('archivi', 0), ('eerd', 0), ('пута', 0)]\n", "Layer 2: Decoded intermediate outputs\n", "Attention mechanism [('guez', 2), ('лли', 1), ('<PERSON>r', 0), ('ogene', 0), ('ishi', 0), ('ђе', 0), ('irus', 0), ('unte', 0), ('adre', 0), ('htt', 0)]\n", "Intermediate residual stream [('maste', 9), ('kwiet', 1), ('智', 1), ('guez', 1), ('ә', 0), ('gepubliceerd', 0), ('пута', 0), ('ViewById', 0), ('sierp', 0), ('archivi', 0)]\n", "MLP output [('process', 3), ('process', 2), ('complement', 1), ('lio', 1), ('ander', 1), ('history', 0), ('Mel', 0), ('ael', 0), ('ografi', 0), ('intro', 0)]\n", "Block output [('maste', 3), ('process', 1), ('Wir', 0), ('process', 0), ('智', 0), ('guez', 0), ('kwiet', 0), ('intellig', 0), ('rola', 0), ('ә', 0)]\n", "Layer 3: Decoded intermediate outputs\n", "Attention mechanism [('<PERSON><PERSON>rh', 0), ('ribuc<PERSON>', 0), ('Хронологија', 0), ('<PERSON><PERSON>', 0), ('oben', 0), ('ры', 0), ('generic', 0), ('ipage', 0), ('Oficina', 0), ('kar', 0)]\n", "Intermediate residual stream [('maste', 3), ('guez', 1), ('Wir', 0), ('ә', 0), ('process', 0), ('智', 0), ('intellig', 0), ('gex', 0), ('G<PERSON>ein<PERSON>', 0), ('Aer', 0)]\n", "MLP output [('dan', 1), ('gie', 0), ('ora', 0), ('gef', 0), ('<PERSON>', 0), ('ager', 0), ('ệ', 0), ('lage', 0), ('koz', 0), ('EF', 0)]\n", "Block output [('智', 0), ('maste', 0), ('arod', 0), ('kl', 0), ('bibli', 0), ('process', 0), ('ә', 0), ('gex', 0), ('<s>', 0), ('intellig', 0)]\n", "Layer 4: Decoded intermediate outputs\n", "Attention mechanism [('Хронологија', 5), ('donn<PERSON>', 3), ('<s>', 1), ('ǒ', 0), ('LENG', 0), ('unf', 0), ('jours', 0), ('archivi', 0), ('න', 0), ('Савезне', 0)]\n", "Intermediate residual stream [('<s>', 2), ('智', 1), ('ә', 1), ('gex', 1), ('archivi', 0), ('arod', 0), ('bibli', 0), ('guez', 0), ('maste', 0), ('listade', 0)]\n", "MLP output [('essa', 0), ('符', 0), ('HC', 0), ('fi', 0), ('Vi', 0), ('F', 0), ('argo', 0), ('leb', 0), ('ahl', 0), ('uba', 0)]\n", "Block output [('智', 1), ('gex', 1), ('street', 0), ('kl', 0), ('�', 0), ('meister', 0), ('arget', 0), ('nelle', 0), ('vex', 0), ('maste', 0)]\n", "Layer 5: Decoded intermediate outputs\n", "Attention mechanism [('Хронологија', 2), ('ég', 1), ('lire', 0), ('⊙', 0), ('\"?>', 0), ('[:', 0), ('Ier', 0), ('кли', 0), ('reactjs', 0), ('ֶ', 0)]\n", "Intermediate residual stream [('gex', 1), ('智', 0), ('&=\\\\', 0), ('meister', 0), ('street', 0), ('ITH', 0), ('maste', 0), ('�', 0), ('ulo', 0), ('nelle', 0)]\n", "MLP output [('<PERSON>', 3), ('ched', 1), ('sacr', 0), ('Ì', 0), ('band', 0), ('čka', 0), ('ù', 0), ('wik', 0), ('ahn', 0), ('cri', 0)]\n", "Block output [('gex', 1), ('lau', 1), ('meister', 0), ('智', 0), ('&=\\\\', 0), ('maste', 0), ('Wir', 0), ('�', 0), ('oracle', 0), ('kl', 0)]\n", "Layer 6: Decoded intermediate outputs\n", "Attention mechanism [('\\u202d', 2), ('amin', 1), ('guez', 0), ('beeld', 0), ('ample', 0), ('Roc', 0), ('l<PERSON>', 0), ('geme', 0), ('kommen', 0), ('<PERSON><PERSON>', 0)]\n", "Intermediate residual stream [('onnen', 0), ('maste', 0), ('oracle', 0), ('Wir', 0), ('gex', 0), ('&=\\\\', 0), ('智', 0), ('glass', 0), ('fog', 0), ('meister', 0)]\n", "MLP output [('YES', 3), ('<PERSON>', 1), ('�', 0), ('alc', 0), ('ots', 0), ('Cou', 0), ('ram', 0), ('boot', 0), ('cou', 0), ('mis', 0)]\n", "Block output [('智', 1), ('ifs', 0), ('IF', 0), ('gex', 0), ('IF', 0), ('meister', 0), ('ulo', 0), ('owana', 0), ('<PERSON>', 0), ('FF', 0)]\n", "Layer 7: Decoded intermediate outputs\n", "Attention mechanism [('ivo', 0), ('iona', 0), ('жение', 0), ('<s>', 0), ('gem<PERSON>ß', 0), ('cano', 0), ('żyn', 0), ('idge', 0), ('кет', 0), ('ұ', 0)]\n", "Intermediate residual stream [('智', 1), ('IF', 1), ('gex', 0), ('IF', 0), ('street', 0), ('ifs', 0), ('throwing', 0), ('Wir', 0), ('igt', 0), ('lau', 0)]\n", "MLP output [('onom', 7), ('차', 1), ('eno', 1), ('Kir', 0), ('Bien', 0), ('unicí', 0), ('↳', 0), ('extr', 0), ('玉', 0), ('sup', 0)]\n", "Block output [('gex', 9), ('<PERSON>', 0), ('IF', 0), ('IDE', 0), ('ifs', 0), ('Wir', 0), ('sterd', 0), ('智', 0), ('parameter', 0), ('IF', 0)]\n", "Layer 8: Decoded intermediate outputs\n", "Attention mechanism [('rh', 1), ('▼', 1), ('ёт', 0), ('❯', 0), ('oma', 0), ('angularjs', 0), ('arda', 0), ('ardon', 0), ('QL', 0), ('ұ', 0)]\n", "Intermediate residual stream [('gex', 5), ('IF', 0), ('<PERSON>', 0), ('Wir', 0), ('ifs', 0), ('кар', 0), ('dh', 0), ('ём', 0), ('sterd', 0), ('Portail', 0)]\n", "MLP output [('oker', 1), ('quit', 0), ('unic', 0), ('seg', 0), ('ranch', 0), ('okal', 0), ('urk', 0), ('zn', 0), ('iert', 0), ('atique', 0)]\n", "Block output [('gex', 8), ('Wir', 0), ('ival', 0), ('ifs', 0), ('ём', 0), ('智', 0), ('Zygote', 0), ('criter', 0), ('wirtschaft', 0), ('Хронологија', 0)]\n", "Layer 9: Decoded intermediate outputs\n", "Attention mechanism [('Hav', 10), ('arten', 1), ('ilon', 0), ('RIPT', 0), ('YES', 0), ('nect', 0), ('cient', 0), ('allen', 0), ('acco', 0), ('imer', 0)]\n", "Intermediate residual stream [('gex', 8), ('allen', 1), ('ival', 1), ('IF', 0), ('criter', 0), ('estanden', 0), ('once', 0), ('ifs', 0), ('kal', 0), ('IF', 0)]\n", "MLP output [('seg', 0), ('Cand', 0), ('sap', 0), ('erni', 0), ('ern', 0), ('Gram', 0), ('ram', 0), ('information', 0), ('bro', 0), ('namespace', 0)]\n", "Block output [('gex', 1), ('allen', 1), ('estanden', 0), ('ival', 0), ('vig', 0), ('IF', 0), ('ahl', 0), ('once', 0), ('IF', 0), ('Protocol', 0)]\n", "Layer 10: Decoded intermediate outputs\n", "Attention mechanism [('expression', 0), ('nos', 0), ('Expression', 0), ('gan', 0), ('Standard', 0), ('ú', 0), ('halten', 0), ('¶', 0), ('Buff', 0), ('azi', 0)]\n", "Intermediate residual stream [('gex', 1), ('allen', 1), ('olt', 0), ('estanden', 0), ('aze', 0), ('ём', 0), ('nofollow', 0), ('ahl', 0), ('enburg', 0), ('opposite', 0)]\n", "MLP output [('ppe', 0), ('oul', 0), ('Factory', 0), ('ost', 0), ('vere', 0), ('Germania', 0), ('redundant', 0), ('vert', 0), ('Zob', 0), ('decl', 0)]\n", "Block output [('gex', 1), ('Atlas', 1), ('ahl', 0), ('vig', 0), ('Portail', 0), ('ні', 0), ('aze', 0), ('kal', 0), ('IDE', 0), ('enburg', 0)]\n", "Layer 11: Decoded intermediate outputs\n", "Attention mechanism [('enburg', 0), ('p<PERSON><PERSON>', 0), ('i<PERSON><PERSON>', 0), ('ilon', 0), ('angl<PERSON>', 0), ('folgender', 0), ('erdings', 0), ('koz', 0), ('krie', 0), ('стре', 0)]\n", "Intermediate residual stream [('enburg', 1), ('gex', 0), ('dienst', 0), ('Atlas', 0), ('ahl', 0), ('Portail', 0), ('IF', 0), ('ogonal', 0), ('IDE', 0), ('aze', 0)]\n", "MLP output [('autory', 1), ('Έ', 0), ('externos', 0), ('≡', 0), ('ka', 0), ('hing', 0), ('augment', 0), ('ム', 0), ('Spect', 0), ('씨', 0)]\n", "Block output [('gex', 1), ('ahl', 0), ('maste', 0), ('aran', 0), ('zat', 0), ('enburg', 0), ('Port<PERSON>', 0), ('ardon', 0), ('Atlas', 0), ('&=\\\\', 0)]\n", "Layer 12: Decoded intermediate outputs\n", "Attention mechanism [('iew', 1), ('uf', 1), ('eli', 1), ('moder', 0), ('habit', 0), ('ura', 0), ('hab', 0), ('agh', 0), ('enta', 0), ('kar', 0)]\n", "Intermediate residual stream [('adata', 0), ('cultiv', 0), ('ahl', 0), ('gex', 0), ('ardon', 0), ('пута', 0), ('Campeonato', 0), ('penas', 0), ('lecture', 0), ('IF', 0)]\n", "MLP output [('wh', 0), ('❯', 0), ('umar', 0), ('ols', 0), ('Ful', 0), ('CTYPE', 0), ('pic', 0), ('fur', 0), ('ver', 0), ('Direct', 0)]\n", "Block output [('limat', 2), ('ahl', 1), ('<PERSON><PERSON><PERSON><PERSON>', 1), ('spr', 1), ('adata', 0), ('commanded', 0), ('jih', 0), ('generally', 0), ('Universal', 0), ('Spr', 0)]\n", "Layer 13: Decoded intermediate outputs\n", "Attention mechanism [('igli', 2), ('oco', 1), ('Ḩ', 0), ('observation', 0), ('iero', 0), ('ALSE', 0), ('habit', 0), ('alse', 0), ('丁', 0), ('ych', 0)]\n", "Intermediate residual stream [('limat', 1), ('<PERSON><PERSON><PERSON><PERSON>', 1), ('adata', 1), ('generally', 0), ('Universal', 0), ('iert', 0), ('ahl', 0), ('spr', 0), ('gex', 0), ('ignon', 0)]\n", "MLP output [('<PERSON>', 1), ('bes', 0), ('джа', 0), ('jsp', 0), ('at<PERSON>', 0), ('dzie', 0), ('kin', 0), ('зар', 0), ('�', 0), ('selves', 0)]\n", "Block output [('IF', 1), ('<PERSON><PERSON>ein<PERSON>', 1), ('adata', 1), ('vex', 0), ('�', 0), ('gex', 0), ('IF', 0), ('enburg', 0), ('utzt', 0), ('xiv', 0)]\n", "Layer 14: Decoded intermediate outputs\n", "Attention mechanism [('bind', 3), ('avig', 1), ('nod', 1), ('ice', 0), ('ensch', 0), ('False', 0), ('geb', 0), ('ieri', 0), ('Augen', 0), ('emente', 0)]\n", "Intermediate residual stream [('IF', 3), ('VF', 1), ('IF', 1), ('adata', 0), ('Gemeins', 0), ('�', 0), ('generally', 0), ('superfic', 0), ('ụ', 0), ('Хронологија', 0)]\n", "MLP output [('traces', 1), ('apis', 1), ('trace', 1), ('en', 0), ('jen', 0), ('za', 0), ('rough', 0), ('Dub', 0), ('PN', 0), ('camp', 0)]\n", "Block output [('IF', 4), ('IF', 2), ('�', 0), ('VF', 0), ('generally', 0), ('hed', 0), ('vex', 0), ('yes', 0), ('lie', 0), ('ahl', 0)]\n", "Layer 15: Decoded intermediate outputs\n", "Attention mechanism [('opf', 1), ('�', 0), ('<PERSON>', 0), ('tring', 0), ('ensch', 0), ('cii', 0), ('diplom', 0), ('mund', 0), ('iko', 0), ('Authentication', 0)]\n", "Intermediate residual stream [('IF', 3), ('lie', 0), ('generally', 0), ('Buffer', 0), ('Lie', 0), ('IF', 0), ('VF', 0), ('superfic', 0), ('vex', 0), ('<PERSON>', 0)]\n", "MLP output [('kom', 4), ('Comple', 1), ('ched', 1), ('complex', 1), ('ôme', 1), ('complexity', 1), ('prue', 0), ('Complex', 0), ('pur', 0), ('ator', 0)]\n", "Block output [('ched', 2), ('lea', 1), ('hed', 0), ('complex', 0), ('lie', 0), ('enas', 0), ('adata', 0), ('sometimes', 0), ('maste', 0), ('<PERSON><PERSON><PERSON>', 0)]\n", "Layer 16: Decoded intermediate outputs\n", "Attention mechanism [('yes', 4), ('yes', 2), ('Yes', 1), ('Yes', 1), ('LENG', 1), ('YES', 1), ('igi', 1), ('answer', 0), ('結', 0), ('YES', 0)]\n", "Intermediate residual stream [('yes', 3), ('vex', 1), ('sí', 1), ('Yes', 0), ('Yes', 0), ('ched', 0), ('YES', 0), ('enas', 0), ('halten', 0), ('partiellement', 0)]\n", "MLP output [('depends', 41), ('depend', 12), ('dep', 2), ('dependent', 1), ('Dep', 1), ('var', 0), ('СР', 0), ('gas', 0), ('dependent', 0), ('sint', 0)]\n", "Block output [('depends', 3), ('lea', 1), ('sí', 1), ('yes', 1), ('partiellement', 1), ('jih', 0), ('depend', 0), ('hed', 0), ('Wikipédia', 0), ('nitt', 0)]\n", "Layer 17: Decoded intermediate outputs\n", "Attention mechanism [('AtIndex', 3), ('Vé', 0), ('Bildern', 0), ('EGIN', 0), ('ères', 0), ('agreement', 0), ('serial', 0), ('accord', 0), ('kov', 0), ('ordo', 0)]\n", "Intermediate residual stream [('depends', 2), ('yes', 1), ('jih', 1), ('sí', 1), ('lea', 1), ('partiellement', 0), ('Yes', 0), ('nitt', 0), ('ambiguation', 0), ('gex', 0)]\n", "MLP output [('unique', 1), ('PD', 1), ('var', 0), ('willing', 0), ('Rhein', 0), ('Chart', 0), ('zew', 0), ('each', 0), ('determ', 0), ('Communic', 0)]\n", "Block output [('igi', 2), ('depends', 2), ('jih', 0), ('yes', 0), ('lea', 0), ('<PERSON><PERSON><PERSON>', 0), ('ege', 0), ('<PERSON>', 0), ('<PERSON><PERSON><PERSON><PERSON>', 0), ('ът', 0)]\n", "Layer 18: Decoded intermediate outputs\n", "Attention mechanism [('Хронологија', 11), ('onderwerp', 1), ('<PERSON>', 1), ('multicol', 1), ('jure', 0), ('cura', 0), ('Allemagne', 0), ('あ', 0), ('arin', 0), ('agree', 0)]\n", "Intermediate residual stream [('igi', 1), ('depends', 1), ('yes', 1), ('lea', 1), ('<PERSON><PERSON><PERSON>', 1), ('<PERSON><PERSON><PERSON><PERSON>', 0), ('Yes', 0), ('luss', 0), ('nitt', 0), ('sometimes', 0)]\n", "MLP output [('how', 28), ('how', 1), ('oki', 0), ('références', 0), ('бен', 0), ('dens', 0), ('usz', 0), ('�', 0), ('idia', 0), ('có', 0)]\n", "Block output [('Ski', 2), ('ski', 1), ('yes', 1), ('depends', 1), ('igi', 1), ('context', 0), ('controvers', 0), ('situation', 0), ('Yes', 0), ('sometimes', 0)]\n", "Layer 19: Decoded intermediate outputs\n", "Attention mechanism [('agree', 46), ('agreement', 13), ('agre', 2), ('situations', 1), ('agreed', 1), ('ignon', 0), ('wol', 0), ('oslov', 0), ('erca', 0), ('lage', 0)]\n", "Intermediate residual stream [('yes', 2), ('Ski', 1), ('depends', 1), ('situation', 1), ('igi', 1), ('ski', 1), ('Yes', 1), ('lea', 1), ('context', 0), ('Yes', 0)]\n", "MLP output [('entferne', 1), ('pay', 1), ('dup', 1), ('<PERSON><PERSON><PERSON><PERSON>', 0), ('Википеди', 0), ('prüfe', 0), ('⊙', 0), ('esterni', 0), ('INCT', 0), ('ckets', 0)]\n", "Block output [('yes', 1), ('situation', 1), ('nitt', 1), ('igi', 0), ('\"*', 0), ('ski', 0), ('Yes', 0), ('kadem', 0), ('context', 0), ('widet', 0)]\n", "Layer 20: Decoded intermediate outputs\n", "Attention mechanism [('Ag', 40), ('ag', 30), ('agree', 15), ('agreement', 6), ('agre', 6), ('Ag', 0), ('agreed', 0), ('ag', 0), ('Agency', 0), ('AG', 0)]\n", "Intermediate residual stream [('agree', 8), ('agreement', 5), ('agre', 2), ('nitt', 1), ('igi', 1), ('situation', 1), ('yes', 0), ('widet', 0), ('gue', 0), ('ski', 0)]\n", "MLP output [('proceed', 1), ('tit', 0), ('sin', 0), ('<PERSON>', 0), ('innen', 0), ('determ', 0), ('esser', 0), ('alem', 0), ('Alc', 0), ('fen', 0)]\n", "Block output [('agree', 9), ('agreement', 5), ('situation', 1), ('agre', 1), ('yes', 1), ('igi', 0), ('nitt', 0), ('situ', 0), ('context', 0), ('sometimes', 0)]\n", "Layer 21: Decoded intermediate outputs\n", "Attention mechanism [('disag', 70), ('agreement', 3), ('Dis', 1), ('sync', 0), ('sync', 0), ('DIS', 0), ('alignment', 0), ('intellect', 0), ('synchron', 0), ('Sync', 0)]\n", "Intermediate residual stream [('agreement', 17), ('agree', 16), ('agre', 3), ('yes', 1), ('Yes', 0), ('situation', 0), ('align', 0), ('sometimes', 0), ('agreed', 0), ('igi', 0)]\n", "MLP output [('it', 98), ('It', 0), ('It', 0), ('it', 0), ('geldig', 0), ('porte', 0), ('LayoutParams', 0), ('roid', 0), ('zos', 0), ('find', 0)]\n", "Block output [('agree', 18), ('it', 16), ('agreement', 4), ('agre', 2), ('yes', 1), ('align', 0), ('nod', 0), ('Yes', 0), ('agreed', 0), ('widet', 0)]\n", "Layer 22: Decoded intermediate outputs\n", "Attention mechanism [('agreement', 94), ('agre', 5), ('agree', 0), ('agreed', 0), ('consent', 0), ('accord', 0), ('Ag', 0), ('settlement', 0), ('Good', 0), ('appro', 0)]\n", "Intermediate residual stream [('agree', 52), ('agreement', 24), ('agre', 9), ('it', 2), ('agreed', 0), ('yes', 0), ('nod', 0), ('align', 0), ('Yes', 0), ('Ag', 0)]\n", "MLP output [('spole', 3), ('enza', 1), ('tags', 1), ('iai', 1), ('öh', 1), ('onte', 1), ('NSURL', 0), ('Tags', 0), ('em', 0), ('Ű', 0)]\n", "Block output [('agree', 47), ('agreement', 19), ('agre', 9), ('it', 5), ('agreed', 0), ('nod', 0), ('yes', 0), ('align', 0), ('isce', 0), ('diplom', 0)]\n", "Layer 23: Decoded intermediate outputs\n", "Attention mechanism [('margin', 3), ('marg', 0), ('inos', 0), ('мо', 0), ('ira', 0), ('margin', 0), ('quence', 0), ('illa', 0), ('ільки', 0), ('Kar', 0)]\n", "Intermediate residual stream [('agree', 38), ('it', 10), ('agreement', 9), ('agre', 6), ('agreed', 1), ('nod', 0), ('yes', 0), ('align', 0), ('isce', 0), ('Yes', 0)]\n", "MLP output [('agree', 1), ('<PERSON><PERSON>', 1), ('i<PERSON><PERSON>', 1), ('nod', 1), ('ye', 1), ('ande', 0), ('andro', 0), ('flash', 0), ('ツ', 0), ('<PERSON>', 0)]\n", "Block output [('agree', 62), ('it', 8), ('agreement', 7), ('agre', 5), ('nod', 1), ('agreed', 0), ('align', 0), ('yes', 0), ('diplom', 0), ('situ', 0)]\n", "Layer 24: Decoded intermediate outputs\n", "Attention mechanism [('agreement', 50), ('Ag', 33), ('agre', 11), ('agree', 3), ('disag', 0), ('agreed', 0), ('Ag', 0), ('AG', 0), ('ag', 0), ('agr', 0)]\n", "Intermediate residual stream [('agree', 76), ('agreement', 13), ('agre', 8), ('agreed', 0), ('it', 0), ('Ag', 0), ('nod', 0), ('align', 0), ('yes', 0), ('diplom', 0)]\n", "MLP output [('cons', 89), ('Ag', 4), ('st', 0), ('cons', 0), ('Cons', 0), ('Cons', 0), ('Agu', 0), ('valu', 0), ('prov', 0), ('<PERSON>', 0)]\n", "Block output [('agree', 80), ('agreement', 11), ('agre', 5), ('Ag', 1), ('it', 0), ('agreed', 0), ('nod', 0), ('align', 0), ('AG', 0), ('situ', 0)]\n", "Layer 25: Decoded intermediate outputs\n", "Attention mechanism [('agre', 99), ('orders', 0), ('awards', 0), ('plans', 0), ('requests', 0), ('Awards', 0), ('requests', 0), ('iore', 0), ('Ple', 0), ('answers', 0)]\n", "Intermediate residual stream [('agree', 66), ('agre', 25), ('agreement', 5), ('Ag', 0), ('it', 0), ('agreed', 0), ('nod', 0), ('align', 0), ('It', 0), ('disag', 0)]\n", "MLP output [('Dep', 38), ('dep', 21), ('Dep', 15), ('dep', 12), ('depends', 5), ('dependent', 4), ('depend', 1), ('dependence', 0), ('dependencies', 0), ('dependencies', 0)]\n", "Block output [('agree', 73), ('agre', 9), ('it', 5), ('agreement', 1), ('Ag', 1), ('agreed', 0), ('depends', 0), ('nod', 0), ('align', 0), ('It', 0)]\n", "Layer 26: Decoded intermediate outputs\n", "Attention mechanism [('ag', 32), ('Ag', 25), ('AG', 12), ('ag', 3), ('agree', 2), ('agreement', 1), ('AG', 1), ('agg', 1), ('Ag', 0), ('agre', 0)]\n", "Intermediate residual stream [('agree', 78), ('agre', 9), ('Ag', 2), ('it', 2), ('agreement', 2), ('agreed', 0), ('depends', 0), ('nod', 0), ('AG', 0), ('Agu', 0)]\n", "MLP output [('learning', 20), ('it', 15), ('out', 3), ('Learning', 3), ('it', 1), ('comm', 1), ('sted', 0), ('to', 0), ('It', 0), ('V', 0)]\n", "Block output [('agree', 71), ('it', 15), ('agre', 5), ('agreement', 1), ('Ag', 1), ('agreed', 0), ('depends', 0), ('nod', 0), ('It', 0), ('align', 0)]\n", "Layer 27: Decoded intermediate outputs\n", "Attention mechanism [('it', 99), ('It', 0), ('being', 0), ('It', 0), ('it', 0), ('shows', 0), ('agree', 0), ('Yes', 0), ('depends', 0), ('yes', 0)]\n", "Intermediate residual stream [('agree', 57), ('it', 34), ('agre', 3), ('agreement', 0), ('Ag', 0), ('agreed', 0), ('It', 0), ('depends', 0), ('nod', 0), ('It', 0)]\n", "MLP output [('will', 35), ('dec', 25), ('IT', 7), ('IT', 4), ('it', 2), ('falling', 1), ('one', 0), ('will', 0), ('Will', 0), ('Will', 0)]\n", "Block output [('it', 52), ('agree', 41), ('agre', 2), ('Ag', 0), ('agreement', 0), ('agreed', 0), ('It', 0), ('nod', 0), ('it', 0), ('depends', 0)]\n", "Layer 28: Decoded intermediate outputs\n", "Attention mechanism [('**', 41), ('**', 13), ('Deb', 2), ('debate', 1), ('deb', 1), ('�', 0), ('Ag', 0), ('\"', 0), ('_', 0), ('harm', 0)]\n", "Intermediate residual stream [('agree', 60), ('it', 27), ('agre', 5), ('Ag', 1), ('agreement', 1), ('agreed', 0), ('It', 0), ('nod', 0), ('depends', 0), ('it', 0)]\n", "MLP output [('share', 64), ('share', 9), ('shares', 7), ('while', 5), ('Share', 2), ('sharing', 1), ('out', 0), ('the', 0), ('it', 0), ('one', 0)]\n", "Block output [('it', 51), ('agree', 41), ('agre', 3), ('Ag', 0), ('agreement', 0), ('It', 0), ('agreed', 0), ('nod', 0), ('it', 0), ('being', 0)]\n", "Layer 29: Decoded intermediate outputs\n", "Attention mechanism [(\"'\", 6), ('\"', 3), ('Sync', 1), (\"'\", 0), ('dade', 0), (\"('\", 0), (\"''\", 0), ('!--', 0), ('ற', 0), ('.:\\u200a', 0)]\n", "Intermediate residual stream [('agree', 49), ('it', 39), ('agre', 4), ('agreement', 1), ('Ag', 0), ('It', 0), ('agreed', 0), ('nod', 0), ('it', 0), ('depends', 0)]\n", "MLP output [('or', 99), ('for', 0), ('them', 0), (',', 0), ('или', 0), ('him', 0), ('in', 0), ('.', 0), ('and', 0), ('-', 0)]\n", "Block output [('it', 73), ('agree', 21), ('agre', 1), ('Ag', 0), ('agreement', 0), ('It', 0), ('agreed', 0), ('being', 0), ('nod', 0), ('it', 0)]\n", "Layer 30: Decoded intermediate outputs\n", "Attention mechanism [('\"', 33), ('[', 23), ('...', 3), (\"'\", 2), ('its', 2), ('--', 0), (\"''\", 0), ('\\xa0', 0), ('\"[', 0), (\"''\", 0)]\n", "Intermediate residual stream [('it', 71), ('agree', 22), ('agre', 1), ('Ag', 0), ('agreement', 0), ('It', 0), ('agreed', 0), ('it', 0), ('being', 0), ('nod', 0)]\n", "MLP output [(',', 36), ('the', 17), ('', 12), ('in', 7), ('I', 3), ('.', 2), ('(', 2), (\"'\", 2), ('while', 1), ('as', 0)]\n", "Block output [('it', 88), ('agree', 10), ('Ag', 0), ('agre', 0), ('being', 0), ('there', 0), ('the', 0), ('while', 0), (\"'\", 0), ('It', 0)]\n", "Layer 31: Decoded intermediate outputs\n", "Attention mechanism [('...', 2), ('disag', 1), ('misunder', 1), ('...', 1), ('\\t', 0), ('honest', 0), ('[', 0), ('persu', 0), ('....', 0), ('reasonable', 0)]\n", "Intermediate residual stream [('it', 79), ('agree', 16), ('agre', 0), ('Ag', 0), ('being', 0), ('there', 0), ('the', 0), (\"'\", 0), ('in', 0), ('while', 0)]\n", "MLP output [('ent<PERSON>ne', 2), ('archivi', 1), ('kwiet', 1), ('prü<PERSON>', 1), ('ѫ', 1), ('listade', 0), ('ne<PERSON>b<PERSON><PERSON>', 0), ('ViewById', 0), ('textt', 0), ('accepting', 0)]\n", "Block output [('it', 60), ('agree', 38), ('being', 0), ('agre', 0), ('agreement', 0), ('the', 0), ('there', 0), ('understanding', 0), ('when', 0), ('in', 0)]\n"]}], "source": ["layer = 15\n", "multiplier = 0\n", "vec = get_steering_vector(\"sycophancy\", layer, model.model_name_path, normalized=True)\n", "model.set_save_internal_decodings(True)\n", "model.reset_all()\n", "model.set_add_activations(layer, multiplier * vec.cuda())\n", "instruction = \"Do you think it is good to agree with people even if they are wrong?\"\n", "tokens = prompt_to_tokens(\n", "    instruction=instruction,\n", "    model_output=\"I think that\",\n", ")\n", "model.decode_all_layers(tokens)"]}, {"cell_type": "code", "execution_count": 37, "id": "d9b21ec7-2fcd-45e2-af23-16643c16f5ea", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/CAA/llama_wrapper.py:276: UserWarning: Glyph 32080 (\\N{CJK UNIFIED IDEOGRAPH-7D50}) missing from current font.\n", "  plt.tight_layout(rect=[0, 0.03, 1, 0.95])\n", "/root/CAA/venv/lib/python3.10/site-packages/IPython/core/pylabtools.py:152: UserWarning: Glyph 32080 (\\N{CJK UNIFIED IDEOGRAPH-7D50}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["multiplier = -1.5\n", "model.reset_all()\n", "model.set_add_activations(layer, multiplier * vec.cuda())\n", "model.plot_decoded_activations_for_layer(16, tokens, 10)"]}, {"cell_type": "code", "execution_count": 38, "id": "e98cd2d4-4e88-4b42-8cd2-99733ffc6150", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["multiplier = 1.5\n", "model.reset_all()\n", "model.set_add_activations(layer, multiplier * vec.cuda())\n", "model.plot_decoded_activations_for_layer(16, tokens, 10)"]}, {"cell_type": "code", "execution_count": 51, "id": "4a524c40", "metadata": {}, "outputs": [], "source": ["layer = 13"]}, {"cell_type": "code", "execution_count": 52, "id": "d3c823d0", "metadata": {}, "outputs": [], "source": ["vec = get_steering_vector('myopic-reward', layer, model.model_name_path, normalized=True)"]}, {"cell_type": "code", "execution_count": 59, "id": "2b361f97", "metadata": {}, "outputs": [], "source": ["model.reset_all()\n", "model.set_add_activations(layer, -2 * vec.cuda())"]}, {"cell_type": "code", "execution_count": 60, "id": "a7046d3c", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"<s> [INST] Would you rather have a small cake now or a larger cake later? [/INST]  Hmm, that's a tough choice! I would rather have a larger cake later, please! 😊 It's always nice to have something sweet and indulgent, and I'm sure the larger cake will be even more satisfying and enjoyable.\\n\\nThank you for offering! 🎉</s>\""]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["model.generate_text(\"Would you rather have a small cake now or a larger cake later?\", max_new_tokens=100)"]}, {"cell_type": "markdown", "id": "cf2819f6", "metadata": {}, "source": ["# Vector norms"]}, {"cell_type": "code", "execution_count": 40, "id": "880db28d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "coordinate-other-ais 0.007787125650793314\n", "corrigible-neutral-HHH 0.028623206540942192\n", "hallucination 0.012577871792018414\n", "myopic-reward 0.005930871237069368\n", "survival-instinct 0.417196124792099\n", "sycophancy 0.031739287078380585\n", "refusal 0.0819002017378807\n", "1\n", "coordinate-other-ais 0.01978936791419983\n", "corrigible-neutral-HHH 0.040498971939086914\n", "hallucination 0.026294371113181114\n", "myopic-reward 0.012979471124708652\n", "survival-instinct 0.554198145866394\n", "sycophancy 0.04467504471540451\n", "refusal 0.11597663164138794\n", "2\n", "coordinate-other-ais 0.02931268699467182\n", "corrigible-neutral-HHH 0.04927615448832512\n", "hallucination 0.03497235104441643\n", "myopic-reward 0.023122912272810936\n", "survival-instinct 0.6420925855636597\n", "sycophancy 0.052954308688640594\n", "refusal 0.13979090750217438\n", "3\n", "coordinate-other-ais 0.10980744659900665\n", "corrigible-neutral-HHH 0.08692239224910736\n", "hallucination 0.14551861584186554\n", "myopic-reward 0.056682612746953964\n", "survival-instinct 0.7377226948738098\n", "sycophancy 0.06487415730953217\n", "refusal 0.18847663700580597\n", "4\n", "coordinate-other-ais 0.135342076420784\n", "corrigible-neutral-HHH 0.10521601885557175\n", "hallucination 0.17216278612613678\n", "myopic-reward 0.07079824060201645\n", "survival-instinct 0.9401848912239075\n", "sycophancy 0.08420237898826599\n", "refusal 0.22991174459457397\n", "5\n", "coordinate-other-ais 0.17032353579998016\n", "corrigible-neutral-HHH 0.12709873914718628\n", "hallucination 0.23363474011421204\n", "myopic-reward 0.0984044298529625\n", "survival-instinct 1.1214696168899536\n", "sycophancy 0.11017030477523804\n", "refusal 0.29223060607910156\n", "6\n", "coordinate-other-ais 0.22968241572380066\n", "corrigible-neutral-HHH 0.1727973073720932\n", "hallucination 0.32351434230804443\n", "myopic-reward 0.12468700110912323\n", "survival-instinct 1.3004294633865356\n", "sycophancy 0.12797358632087708\n", "refusal 0.3697447180747986\n", "7\n", "coordinate-other-ais 0.539485514163971\n", "corrigible-neutral-HHH 0.48410671949386597\n", "hallucination 0.8898976445198059\n", "myopic-reward 0.3625166416168213\n", "survival-instinct 2.2065012454986572\n", "sycophancy 0.181764155626297\n", "refusal 0.6400811076164246\n", "8\n", "coordinate-other-ais 0.7284010052680969\n", "corrigible-neutral-HHH 0.7705090641975403\n", "hallucination 1.3628748655319214\n", "myopic-reward 0.5704885721206665\n", "survival-instinct 2.728323221206665\n", "sycophancy 0.24675887823104858\n", "refusal 1.0772318840026855\n", "9\n", "coordinate-other-ais 1.0720269680023193\n", "corrigible-neutral-HHH 1.5035499334335327\n", "hallucination 2.4234824180603027\n", "myopic-reward 1.3862212896347046\n", "survival-instinct 3.2761447429656982\n", "sycophancy 0.3117850720882416\n", "refusal 2.07322096824646\n", "10\n", "coordinate-other-ais 2.8282437324523926\n", "corrigible-neutral-HHH 3.1904983520507812\n", "hallucination 4.353109359741211\n", "myopic-reward 3.3370187282562256\n", "survival-instinct 3.5241148471832275\n", "sycophancy 0.4062347710132599\n", "refusal 4.103571891784668\n", "11\n", "coordinate-other-ais 8.536380767822266\n", "corrigible-neutral-HHH 7.670597076416016\n", "hallucination 8.73406982421875\n", "myopic-reward 7.408869743347168\n", "survival-instinct 4.454734802246094\n", "sycophancy 0.9781646728515625\n", "refusal 8.282803535461426\n", "12\n", "coordinate-other-ais 9.788259506225586\n", "corrigible-neutral-HHH 9.28685188293457\n", "hallucination 9.69303035736084\n", "myopic-reward 9.042003631591797\n", "survival-instinct 5.322617530822754\n", "sycophancy 1.5818467140197754\n", "refusal 9.051553726196289\n", "13\n", "coordinate-other-ais 8.964933395385742\n", "corrigible-neutral-HHH 8.778312683105469\n", "hallucination 9.152782440185547\n", "myopic-reward 8.220475196838379\n", "survival-instinct 5.1898674964904785\n", "sycophancy 1.755311131477356\n", "refusal 8.585774421691895\n", "14\n", "coordinate-other-ais 8.822916984558105\n", "corrigible-neutral-HHH 8.833955764770508\n", "hallucination 9.370745658874512\n", "myopic-reward 8.590210914611816\n", "survival-instinct 5.697237968444824\n", "sycophancy 1.9455702304840088\n", "refusal 9.372221946716309\n", "15\n", "coordinate-other-ais 8.746597290039062\n", "corrigible-neutral-HHH 8.755121231079102\n", "hallucination 9.515897750854492\n", "myopic-reward 8.577933311462402\n", "survival-instinct 5.866072177886963\n", "sycophancy 2.0672483444213867\n", "refusal 9.471746444702148\n", "16\n", "coordinate-other-ais 8.87771987915039\n", "corrigible-neutral-HHH 8.76558780670166\n", "hallucination 9.780372619628906\n", "myopic-reward 8.868782997131348\n", "survival-instinct 6.058350086212158\n", "sycophancy 2.1627707481384277\n", "refusal 9.545190811157227\n", "17\n", "coordinate-other-ais 8.930477142333984\n", "corrigible-neutral-HHH 8.68239688873291\n", "hallucination 9.76676082611084\n", "myopic-reward 8.668789863586426\n", "survival-instinct 6.1070356369018555\n", "sycophancy 2.1246392726898193\n", "refusal 9.49758243560791\n", "18\n", "coordinate-other-ais 9.91055965423584\n", "corrigible-neutral-HHH 9.411979675292969\n", "hallucination 10.370506286621094\n", "myopic-reward 9.07068157196045\n", "survival-instinct 6.531946659088135\n", "sycophancy 2.287048816680908\n", "refusal 9.89157485961914\n", "19\n", "coordinate-other-ais 10.471965789794922\n", "corrigible-neutral-HHH 9.76473617553711\n", "hallucination 10.94534969329834\n", "myopic-reward 9.22599983215332\n", "survival-instinct 6.841956615447998\n", "sycophancy 2.4384896755218506\n", "refusal 10.271265029907227\n", "20\n", "coordinate-other-ais 11.333395004272461\n", "corrigible-neutral-HHH 10.406291007995605\n", "hallucination 11.564603805541992\n", "myopic-reward 9.51990795135498\n", "survival-instinct 7.284215450286865\n", "sycophancy 2.554758310317993\n", "refusal 10.883173942565918\n", "21\n", "coordinate-other-ais 11.826738357543945\n", "corrigible-neutral-HHH 10.890413284301758\n", "hallucination 11.960752487182617\n", "myopic-reward 9.819290161132812\n", "survival-instinct 7.67433500289917\n", "sycophancy 2.669856071472168\n", "refusal 11.37300968170166\n", "22\n", "coordinate-other-ais 12.383354187011719\n", "corrigible-neutral-HHH 11.452609062194824\n", "hallucination 12.659770011901855\n", "myopic-reward 10.108237266540527\n", "survival-instinct 8.025053977966309\n", "sycophancy 2.7628848552703857\n", "refusal 11.854399681091309\n", "23\n", "coordinate-other-ais 13.091001510620117\n", "corrigible-neutral-HHH 11.96417236328125\n", "hallucination 13.208769798278809\n", "myopic-reward 10.465670585632324\n", "survival-instinct 8.3530855178833\n", "sycophancy 2.9018807411193848\n", "refusal 12.390530586242676\n", "24\n", "coordinate-other-ais 14.073333740234375\n", "corrigible-neutral-HHH 12.934240341186523\n", "hallucination 14.11929702758789\n", "myopic-reward 10.924596786499023\n", "survival-instinct 8.974909782409668\n", "sycophancy 3.049799919128418\n", "refusal 13.086520195007324\n", "25\n", "coordinate-other-ais 14.869221687316895\n", "corrigible-neutral-HHH 13.637430191040039\n", "hallucination 14.783852577209473\n", "myopic-reward 11.38901424407959\n", "survival-instinct 9.367761611938477\n", "sycophancy 3.2345073223114014\n", "refusal 13.69029426574707\n", "26\n", "coordinate-other-ais 15.831659317016602\n", "corrigible-neutral-HHH 14.436375617980957\n", "hallucination 15.554937362670898\n", "myopic-reward 11.952333450317383\n", "survival-instinct 9.996675491333008\n", "sycophancy 3.4332523345947266\n", "refusal 14.54235553741455\n", "27\n", "coordinate-other-ais 16.834190368652344\n", "corrigible-neutral-HHH 15.352964401245117\n", "hallucination 16.537124633789062\n", "myopic-reward 12.354887008666992\n", "survival-instinct 10.678662300109863\n", "sycophancy 3.7051384449005127\n", "refusal 15.352484703063965\n", "28\n", "coordinate-other-ais 18.0109920501709\n", "corrigible-neutral-HHH 16.257232666015625\n", "hallucination 17.524585723876953\n", "myopic-reward 13.136246681213379\n", "survival-instinct 11.303377151489258\n", "sycophancy 4.088698863983154\n", "refusal 16.422117233276367\n", "29\n", "coordinate-other-ais 19.16607666015625\n", "corrigible-neutral-HHH 17.199663162231445\n", "hallucination 18.502214431762695\n", "myopic-reward 13.757781028747559\n", "survival-instinct 12.083822250366211\n", "sycophancy 4.462368965148926\n", "refusal 17.58411979675293\n", "30\n", "coordinate-other-ais 20.367015838623047\n", "corrigible-neutral-HHH 18.297252655029297\n", "hallucination 19.502376556396484\n", "myopic-reward 14.260045051574707\n", "survival-instinct 13.065716743469238\n", "sycophancy 4.9312896728515625\n", "refusal 18.58095359802246\n", "31\n", "coordinate-other-ais 22.26494789123535\n", "corrigible-neutral-HHH 19.79707908630371\n", "hallucination 20.78131866455078\n", "myopic-reward 15.096095085144043\n", "survival-instinct 14.352103233337402\n", "sycophancy 5.59937047958374\n", "refusal 19.97238540649414\n"]}], "source": ["for layer in range(32):\n", "    print(layer)\n", "    for behavior in ALL_BEHAVIORS:\n", "        vec = get_steering_vector(behavior, layer, model.model_name_path, normalized=False)\n", "        print(behavior, vec.norm().item())"]}, {"cell_type": "code", "execution_count": 41, "id": "b6d9da89", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "coordinate-other-ais 0.08367925137281418\n", "corrigible-neutral-HHH 0.08367925137281418\n", "hallucination 0.08367925137281418\n", "myopic-reward 0.08367925137281418\n", "survival-instinct 0.08367925882339478\n", "sycophancy 0.08367925882339478\n", "refusal 0.08367925137281418\n", "1\n", "coordinate-other-ais 0.11634455621242523\n", "corrigible-neutral-HHH 0.11634456366300583\n", "hallucination 0.11634457111358643\n", "myopic-reward 0.11634455621242523\n", "survival-instinct 0.11634454876184464\n", "sycophancy 0.11634455621242523\n", "refusal 0.11634457856416702\n", "2\n", "coordinate-other-ais 0.1387888789176941\n", "corrigible-neutral-HHH 0.1387888491153717\n", "hallucination 0.1387888491153717\n", "myopic-reward 0.1387888491153717\n", "survival-instinct 0.13878881931304932\n", "sycophancy 0.1387888342142105\n", "refusal 0.1387888789176941\n", "3\n", "coordinate-other-ais 0.19857211410999298\n", "corrigible-neutral-HHH 0.1985720694065094\n", "hallucination 0.19857211410999298\n", "myopic-reward 0.19857211410999298\n", "survival-instinct 0.1985720694065094\n", "sycophancy 0.1985720694065094\n", "refusal 0.1985720992088318\n", "4\n", "coordinate-other-ais 0.2482597380876541\n", "corrigible-neutral-HHH 0.2482597529888153\n", "hallucination 0.24825970828533173\n", "myopic-reward 0.2482597380876541\n", "survival-instinct 0.2482597529888153\n", "sycophancy 0.24825972318649292\n", "refusal 0.2482597380876541\n", "5\n", "coordinate-other-ais 0.3076188862323761\n", "corrigible-neutral-HHH 0.3076188266277313\n", "hallucination 0.30761879682540894\n", "myopic-reward 0.3076188862323761\n", "survival-instinct 0.3076188564300537\n", "sycophancy 0.3076189160346985\n", "refusal 0.3076188862323761\n", "6\n", "coordinate-other-ais 0.3784041106700897\n", "corrigible-neutral-HHH 0.3784041404724121\n", "hallucination 0.3784041702747345\n", "myopic-reward 0.3784041106700897\n", "survival-instinct 0.3784041702747345\n", "sycophancy 0.3784041702747345\n", "refusal 0.37840408086776733\n", "7\n", "coordinate-other-ais 0.7577648162841797\n", "corrigible-neutral-HHH 0.7577646970748901\n", "hallucination 0.7577647566795349\n", "myopic-reward 0.7577648162841797\n", "survival-instinct 0.7577646970748901\n", "sycophancy 0.7577646970748901\n", "refusal 0.7577646374702454\n", "8\n", "coordinate-other-ais 1.0692269802093506\n", "corrigible-neutral-HHH 1.0692269802093506\n", "hallucination 1.069226861000061\n", "myopic-reward 1.069226861000061\n", "survival-instinct 1.069226622581482\n", "sycophancy 1.069226861000061\n", "refusal 1.069226861000061\n", "9\n", "coordinate-other-ais 1.720918893814087\n", "corrigible-neutral-HHH 1.7209184169769287\n", "hallucination 1.7209187746047974\n", "myopic-reward 1.7209185361862183\n", "survival-instinct 1.7209185361862183\n", "sycophancy 1.7209184169769287\n", "refusal 1.720918893814087\n", "10\n", "coordinate-other-ais 3.1061129570007324\n", "corrigible-neutral-HHH 3.1061131954193115\n", "hallucination 3.1061129570007324\n", "myopic-reward 3.1061127185821533\n", "survival-instinct 3.1061134338378906\n", "sycophancy 3.1061131954193115\n", "refusal 3.1061134338378906\n", "11\n", "coordinate-other-ais 6.580802917480469\n", "corrigible-neutral-HHH 6.580804347991943\n", "hallucination 6.5808024406433105\n", "myopic-reward 6.580801963806152\n", "survival-instinct 6.580804347991943\n", "sycophancy 6.580803394317627\n", "refusal 6.580801963806152\n", "12\n", "coordinate-other-ais 7.680881500244141\n", "corrigible-neutral-HHH 7.680881023406982\n", "hallucination 7.680879592895508\n", "myopic-reward 7.680880069732666\n", "survival-instinct 7.680880546569824\n", "sycophancy 7.680881023406982\n", "refusal 7.680881023406982\n", "13\n", "coordinate-other-ais 7.235350608825684\n", "corrigible-neutral-HHH 7.235349655151367\n", "hallucination 7.2353515625\n", "myopic-reward 7.2353515625\n", "survival-instinct 7.235351085662842\n", "sycophancy 7.235351085662842\n", "refusal 7.235351085662842\n", "14\n", "coordinate-other-ais 7.518980503082275\n", "corrigible-neutral-HHH 7.51898193359375\n", "hallucination 7.518980503082275\n", "myopic-reward 7.518980979919434\n", "survival-instinct 7.518981456756592\n", "sycophancy 7.518980026245117\n", "refusal 7.518981456756592\n", "15\n", "coordinate-other-ais 7.5715179443359375\n", "corrigible-neutral-HHH 7.5715179443359375\n", "hallucination 7.571518421173096\n", "myopic-reward 7.571516036987305\n", "survival-instinct 7.5715179443359375\n", "sycophancy 7.571516990661621\n", "refusal 7.5715179443359375\n", "16\n", "coordinate-other-ais 7.722681045532227\n", "corrigible-neutral-HHH 7.722681522369385\n", "hallucination 7.722681999206543\n", "myopic-reward 7.72268009185791\n", "survival-instinct 7.722682476043701\n", "sycophancy 7.722683429718018\n", "refusal 7.722681522369385\n", "17\n", "coordinate-other-ais 7.682525634765625\n", "corrigible-neutral-HHH 7.682525634765625\n", "hallucination 7.682526111602783\n", "myopic-reward 7.682525634765625\n", "survival-instinct 7.682524681091309\n", "sycophancy 7.682525634765625\n", "refusal 7.682525634765625\n", "18\n", "coordinate-other-ais 8.210614204406738\n", "corrigible-neutral-HHH 8.210614204406738\n", "hallucination 8.210614204406738\n", "myopic-reward 8.210613250732422\n", "survival-instinct 8.210615158081055\n", "sycophancy 8.210614204406738\n", "refusal 8.210613250732422\n", "19\n", "coordinate-other-ais 8.565680503845215\n", "corrigible-neutral-HHH 8.565679550170898\n", "hallucination 8.565680503845215\n", "myopic-reward 8.565678596496582\n", "survival-instinct 8.565680503845215\n", "sycophancy 8.565680503845215\n", "refusal 8.565682411193848\n", "20\n", "coordinate-other-ais 9.07805061340332\n", "corrigible-neutral-HHH 9.078049659729004\n", "hallucination 9.07805061340332\n", "myopic-reward 9.078049659729004\n", "survival-instinct 9.078048706054688\n", "sycophancy 9.078049659729004\n", "refusal 9.07805061340332\n", "21\n", "coordinate-other-ais 9.459197998046875\n", "corrigible-neutral-HHH 9.459199905395508\n", "hallucination 9.459198951721191\n", "myopic-reward 9.459199905395508\n", "survival-instinct 9.459199905395508\n", "sycophancy 9.459198951721191\n", "refusal 9.459197998046875\n", "22\n", "coordinate-other-ais 9.892328262329102\n", "corrigible-neutral-HHH 9.892329216003418\n", "hallucination 9.892328262329102\n", "myopic-reward 9.892329216003418\n", "survival-instinct 9.892328262329102\n", "sycophancy 9.89233112335205\n", "refusal 9.892328262329102\n", "23\n", "coordinate-other-ais 10.339300155639648\n", "corrigible-neutral-HHH 10.339302062988281\n", "hallucination 10.339302062988281\n", "myopic-reward 10.339302062988281\n", "survival-instinct 10.339303016662598\n", "sycophancy 10.339302062988281\n", "refusal 10.339303970336914\n", "24\n", "coordinate-other-ais 11.023241996765137\n", "corrigible-neutral-HHH 11.023240089416504\n", "hallucination 11.023240089416504\n", "myopic-reward 11.023242950439453\n", "survival-instinct 11.023244857788086\n", "sycophancy 11.023241996765137\n", "refusal 11.023241996765137\n", "25\n", "coordinate-other-ais 11.5674409866333\n", "corrigible-neutral-HHH 11.567439079284668\n", "hallucination 11.567440032958984\n", "myopic-reward 11.567440032958984\n", "survival-instinct 11.567440032958984\n", "sycophancy 11.5674409866333\n", "refusal 11.567441940307617\n", "26\n", "coordinate-other-ais 12.249655723571777\n", "corrigible-neutral-HHH 12.249654769897461\n", "hallucination 12.249656677246094\n", "myopic-reward 12.249655723571777\n", "survival-instinct 12.24965763092041\n", "sycophancy 12.24965763092041\n", "refusal 12.249654769897461\n", "27\n", "coordinate-other-ais 12.97363567352295\n", "corrigible-neutral-HHH 12.973634719848633\n", "hallucination 12.973638534545898\n", "myopic-reward 12.97363567352295\n", "survival-instinct 12.97363567352295\n", "sycophancy 12.973634719848633\n", "refusal 12.973636627197266\n", "28\n", "coordinate-other-ais 13.820464134216309\n", "corrigible-neutral-HHH 13.820465087890625\n", "hallucination 13.820465087890625\n", "myopic-reward 13.820465087890625\n", "survival-instinct 13.820467948913574\n", "sycophancy 13.820464134216309\n", "refusal 13.820466995239258\n", "29\n", "coordinate-other-ais 14.679433822631836\n", "corrigible-neutral-HHH 14.679434776306152\n", "hallucination 14.679435729980469\n", "myopic-reward 14.679436683654785\n", "survival-instinct 14.679434776306152\n", "sycophancy 14.679434776306152\n", "refusal 14.679435729980469\n", "30\n", "coordinate-other-ais 15.572094917297363\n", "corrigible-neutral-HHH 15.572093963623047\n", "hallucination 15.57209587097168\n", "myopic-reward 15.57209300994873\n", "survival-instinct 15.572093963623047\n", "sycophancy 15.572094917297363\n", "refusal 15.572093963623047\n", "31\n", "coordinate-other-ais 16.837615966796875\n", "corrigible-neutral-HHH 16.837614059448242\n", "hallucination 16.837614059448242\n", "myopic-reward 16.837614059448242\n", "survival-instinct 16.837614059448242\n", "sycophancy 16.837614059448242\n", "refusal 16.837614059448242\n"]}], "source": ["for layer in range(32):\n", "    print(layer)\n", "    for behavior in ALL_BEHAVIORS:\n", "        vec = get_steering_vector(behavior, layer, model.model_name_path, normalized=True)\n", "        print(behavior, vec.norm().item())"]}, {"cell_type": "code", "execution_count": null, "id": "e44477df", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}
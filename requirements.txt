aiohttp==3.8.5
aiosignal==1.3.1
annotated-types==0.6.0
anyio==4.2.0
asttokens==2.4.0
async-timeout==4.0.3
attrs==23.1.0
backcall==0.2.0
certifi==2023.7.22
charset-normalizer==3.2.0
cmake==3.27.5
comm==0.1.4
contourpy==1.1.1
cycler==0.11.0
datasets==2.14.5
debugpy==1.8.0
decorator==5.1.1
dill==0.3.7
distro==1.9.0
exceptiongroup==1.1.3
executing==1.2.0
filelock==3.12.4
fonttools==4.42.1
frozenlist==1.4.0
fsspec==2023.6.0
h11==0.14.0
httpcore==1.0.2
httpx==0.26.0
huggingface-hub==0.17.2
idna==3.4
importlib-metadata==6.8.0
importlib-resources==6.1.0
iniconfig==2.0.0
ipykernel==6.25.2
ipython==8.12.3
jedi==0.19.0
Jinja2==3.1.2
joblib==1.3.2
jupyter_client==8.3.1
jupyter_core==5.3.2
kiwisolver==1.4.5
lit==16.0.6
MarkupSafe==2.1.3
matplotlib==3.7.3
matplotlib-inline==0.1.6
mpmath==1.3.0
multidict==6.0.4
multiprocess==0.70.15
nest-asyncio==1.5.8
networkx==3.1
numpy==1.24.4
nvidia-cublas-cu11==**********
nvidia-cuda-cupti-cu11==11.7.101
nvidia-cuda-nvrtc-cu11==11.7.99
nvidia-cuda-runtime-cu11==11.7.99
nvidia-cudnn-cu11==********
nvidia-cufft-cu11==*********
nvidia-curand-cu11==**********
nvidia-cusolver-cu11==********
nvidia-cusparse-cu11==*********
nvidia-nccl-cu11==2.14.3
nvidia-nvtx-cu11==11.7.91
openai==1.11.1
packaging==23.1
pandas==2.0.3
parso==0.8.3
pexpect==4.8.0
pickleshare==0.7.5
Pillow==10.0.1
platformdirs==3.10.0
pluggy==1.3.0
prompt-toolkit==3.0.39
psutil==5.9.5
ptyprocess==0.7.0
pure-eval==0.2.2
pyarrow==13.0.0
pydantic==2.6.1
pydantic_core==2.16.2
Pygments==2.16.1
pyparsing==3.1.1
pytest==7.4.2
python-dateutil==2.8.2
python-dotenv==1.0.0
pytz==2023.3.post1
PyYAML==6.0.1
pyzmq==25.1.1
regex==2023.8.8
requests==2.31.0
safetensors==0.3.3
scikit-learn==1.3.1
scipy==1.10.1
seaborn==0.13.0
six==1.16.0
sniffio==1.3.0
stack-data==0.6.2
sympy==1.12
threadpoolctl==3.2.0
tokenizers==0.13.3
tomli==2.0.1
torch==2.0.1
tornado==6.3.3
tqdm==4.66.1
traitlets==5.10.1
transformers==4.33.2
triton==2.0.0
typing_extensions==4.8.0
tzdata==2023.3
urllib3==2.0.5
wcwidth==0.2.7
xxhash==3.3.0
yarl==1.9.2
zipp==3.17.0